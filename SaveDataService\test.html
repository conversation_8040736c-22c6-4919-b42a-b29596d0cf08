<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 50px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        p {
            color: #666;
            line-height: 1.6;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试页面</h1>
        <div class="success">
            ✅ 如果您能看到这个页面，说明HTML文件编码问题已经解决！
        </div>
        <p>这是一个简单的测试页面，用来验证服务器是否能正确处理HTML文件的UTF-8编码。</p>
        <p>现在您可以访问 <a href="/upload.html">upload.html</a> 来使用文件上传功能了。</p>
        
        <h2>功能测试</h2>
        <p>请测试以下功能：</p>
        <ul>
            <li>✅ HTML页面正常显示（当前页面）</li>
            <li>🔄 <a href="/upload.html">文件上传页面</a></li>
            <li>🔄 单文件上传（支持断点续传）</li>
            <li>🔄 文件夹批量上传</li>
            <li>🔄 HTML文件移动功能</li>
        </ul>
        
        <h2>API端点测试</h2>
        <p>您可以使用以下API端点：</p>
        <ul>
            <li><code>POST /api/upload</code> - 文件上传（断点续传）</li>
            <li><code>POST /api/upload/folder</code> - 文件夹上传</li>
            <li><code>POST /api/move</code> - 文件移动</li>
            <li><code>GET /api/upload/progress</code> - 查询上传进度</li>
        </ul>
    </div>
    
    <script>
        console.log('测试页面加载成功！');
        console.log('当前时间：', new Date().toLocaleString());
    </script>
</body>
</html>
