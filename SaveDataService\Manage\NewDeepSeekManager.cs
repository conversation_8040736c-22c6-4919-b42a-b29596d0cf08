using ExcelToData;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using NPOI.HPSF;
using OpenAI.RealtimeConversation;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace SaveDataService.Manage
{
    public class NewDeepSeekManager
    {
        // 私有静态实例变量
        private static NewDeepSeekManager _instance;
        // 线程安全锁对象
        private static readonly object _lock = new object();
        private readonly Kernel _kernel;
        public readonly IChatCompletionService _chatService;
        private readonly ConcurrentDictionary<string, string> _completeResponses = new();
        // 存储每个对话的取消令牌源
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationSources = new();
        // 存储每个对话的暂停状态
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _pauseSemaphores = new();
        // 存储每个对话的状态
        private readonly ConcurrentDictionary<string, ConversationStatus> _conversationStatuses = new();
        // 用于生成唯一ID
        private int _nextId = 1001;

        public enum ConversationStatus
        {
            NotStarted,
            Running,
            Paused,
            Completed,
            Cancelled
        }
        string deepseekApiKey = "0240fd2a-05fc-44c7-bb82-f8af845cc0af";// "sk-2b77e4bfff304444b2e7036ef6082cb1";
        string deepseekModel = "deepseek-r1-250528";// "deepseek-chat";
        string deepseekUri = "https://ark.cn-beijing.volces.com/api/v3";//"https://api.deepseek.com/v1";
        // 添加事件处理机制，当单个对话完成时触发
        public event EventHandler<ChatCompletedEventArgs> ChatCompleted;
        public event EventHandler<ChatStatusChangedEventArgs> ChatStatusChanged;

        // 私有构造函数，防止外部实例化
        private NewDeepSeekManager()
        {
            // 初始化代码
            _kernel = Kernel.CreateBuilder()
.AddOpenAIChatCompletion(
    modelId: deepseekModel, // 模型名称
    apiKey: deepseekApiKey, // 替换为你的 API Key
    endpoint: new Uri(deepseekUri) // DeepSeek API 地址
)
.Build();

            _chatService = _kernel.GetRequiredService<IChatCompletionService>();
        }
        // 公共静态属性，用于访问单例实例
        public static NewDeepSeekManager Instance
        {
            get
            {
                // 双重检查锁定模式确保线程安全
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new NewDeepSeekManager();
                        }
                    }
                }
                return _instance;
            }
        }

        public class ChatCompletedEventArgs : EventArgs
        {
            public string Id { get; set; }
            public string BaseId { get; set; }
            public string Prompt { get; set; }
            public string Response { get; set; }
            public Action<string> CallBack;
        }
        public class ChatStatusChangedEventArgs : EventArgs
        {
            public string Id { get; set; }
            public ConversationStatus Status { get; set; }
        }
        public List<TaskData> taskList = new List<TaskData>();
        string gameBaseJsonKeys = "";
        string ignlinkJsonKeys = "";
        string gameintroduceKeys = "";
        string chapterJsonKeys = "";
        string characterJsonKeys = "";
        string scenceJsonKeys = "";
        string scencekeysJsonKeys = "";
        string scencemusicJsonKeys = "";
        string scenceeffectsJsonKeys = "";
        string scenceskyboxJsonKeys = "";
        public string baseTemplatePath = "baseTemplate";
        public async Task init()
        {

            // 先订阅事件，确保不会错过任何完成通知
            Instance.ChatCompleted += async (sender, e) =>
            {
                Console.ForegroundColor = ConsoleColor.DarkGreen;
                Console.WriteLine($"\n[通知] 对话 {e.Id} 已完成，可以处理结果");
                Console.WriteLine(e.Response);
                Console.ResetColor();
                // 这里可以处理单个对话的结果

            };

            Instance.ChatStatusChanged += (sender, e) =>
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"\n[状态变更] 对话 {e.Id} 状态: {e.Status}");
                Console.ResetColor();

            };

            while (true)
            {
                Console.Write("> ");
                var userInput = Console.ReadLine();
                if (string.IsNullOrWhiteSpace(userInput))
                    continue;
                if (userInput.Trim().Equals("/exit", StringComparison.OrdinalIgnoreCase))
                    break;
                if (userInput.StartsWith("/test"))
                {
                    await RunConcurrentChats((str) =>
                    {
                    });
                }
            }
        }

        /// <summary>
        ///  设置前置需求
        /// </summary>
        /// <param name="chatService"></param>
        /// <param name="prompt"></param>
        /// <returns></returns>
        public async Task GenerateGameBase(string inputPrompt, Action<string> callback)
        {
            ClearTasklist();
            AI_Game_Output item = AI_Game_Output.getByid("10001");
            if (item != null)
            {
                //gameBaseJsonKeys = item.jsonModle;
                if (item.ifParallel == true)
                {
                    List<AI_Game_Output> alllist = AI_Game_Output.getAllDataToList();

                    // 筛选出所有ifJson值与concurrency相同的数据
                    List<AI_Game_Output> filteredList = alllist.Where(x => x.step == item.step).ToList();

                    // 输出筛选结果
                    Console.WriteLine($"找到 {filteredList.Count} 条step为{item.step}的记录:");
                    foreach (var output in filteredList)
                    {
                        //Console.WriteLine($"ID: {output.id}, 描述: {output.templateDescription}");
                        TaskData data = new TaskData();
                        data.id = output.id + "-" + GenerateUUid();
                        data.systemPrompt = output.systemPrompt;
                        data.prompt = inputPrompt;
                        data.baseid = output.id;
                        taskList.Add(data);
                    }
                }
                else
                {
                    string priority = item.priority;
                    string jsonModle = item.jsonModle;
                    string systemprompt = item.systemPrompt;
                    await ChatWithAI(systemprompt, inputPrompt, "GameBase", priority, callback, jsonModle);
                }
                if (taskList.Count > 0)
                {
                    await AddNewConversation(callback);
                }
            }
            else
            {
                Console.WriteLine($"AI_Game_Output 表中未找到10001 的数据！");
            }
        }
        /// <summary>
        /// 生成世界观和游戏目标
        /// </summary>
        /// <param name="callback"></param>
        /// <returns></returns>
        public async Task GenerateWorldViewAndTarget(Action<string> callback)
        {
            ClearTasklist();
            AI_Game_Output item = AI_Game_Output.getByid("10002");
            if (item != null)
            {
                string outputPath = Path.Combine("baseTemplate", "GameBase.json");
                string jsonString = File.ReadAllText(outputPath);
                if (item.ifParallel == true)
                {
                    List<AI_Game_Output> alllist = AI_Game_Output.getAllDataToList();

                    // 筛选出所有ifJson值与concurrency相同的数据
                    List<AI_Game_Output> filteredList = alllist.Where(x => x.step == item.step).ToList();

                    // 输出筛选结果
                    Console.WriteLine($"找到 {filteredList.Count} 条step为{item.step}的记录:");

                    foreach (var output in filteredList)
                    {
                        //Console.WriteLine($"ID: {output.id}, 描述: {output.templateDescription}");
                        TaskData data = new TaskData();
                        data.id = output.id + "-" + GenerateUUid();
                        data.systemPrompt = output.systemPrompt;
                        data.prompt = jsonString;
                        data.baseid = output.id;
                        taskList.Add(data);
                    }
                }
                else
                {
                    string priority = item.priority;
                    string jsonModle = item.jsonModle;
                    string systemprompt = item.systemPrompt;
                    await ChatWithAI(systemprompt, jsonString, "worldviewandtarget", priority, callback, jsonModle);
                }
                if (taskList.Count > 0)
                {
                    await AddNewConversation(callback);
                }
            }

        }
        /// <summary>
        /// 玩家二次修改基本参数后 重新生成
        /// </summary>
        /// <param name="theme"></param>
        /// <param name="gametype"></param>
        /// <param name="gameangleOfview"></param>
        /// <param name="dimension"></param>
        /// <param name="worldview"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public async Task<string> RefreshGameBaseType(string theme, string gametype, string gameangleOfview, string dimension, string worldview)
        {
            string outstr = "";
            AI_Game_Output item = AI_Game_Output.getByid("10003");
            string prompt = "";
            if (item != null)
            {
                //gameBaseJsonKeys = item.jsonModle;
                prompt = item.generateTemplate;
                prompt = Regex.Replace(prompt, @"\{PlayerInt_题材\}", theme);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_游戏类型\}", gametype);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_游戏视角\}", gameangleOfview);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_dimension\}", dimension);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_世界观\}", worldview);

                var chatHistory = new ChatHistory();
                chatHistory.AddUserMessage(prompt); // 添加用户输入到历史

                var generatedContent = new StringBuilder();
                await foreach (var chunk in _chatService.GetStreamingChatMessageContentsAsync(chatHistory))
                {
                    Console.Write(chunk.Content);
                    generatedContent.Append(chunk.Content);
                }

                // 从生成内容中提取游戏名（假设第一行是游戏名）
                string generatedText = generatedContent.ToString();
                string gameNameLine = generatedText.Split('\n').FirstOrDefault() ?? "游戏名：《未知》";
                // 构建基础信息
                StringBuilder newresult = new StringBuilder();
                newresult.AppendLine(gameNameLine); // 使用生成内容中的游戏名
                newresult.AppendLine($"题材：{theme}");
                newresult.AppendLine($"游戏类型：{gametype}");
                newresult.AppendLine($"游戏视角：{gameangleOfview}");
                newresult.AppendLine($"3D或2D：{dimension}");
                //newresult.AppendLine($"世界观：{worldview}");
                // 添加生成内容中除第一行（游戏名）外的其余内容
                string remainingContent = string.Join("\n", generatedText.Split('\n').Skip(1));
                //newresult.AppendLine(remainingContent);

                StringBuilder result1 = new StringBuilder();
                result1.AppendLine($"世界观：{worldview}");
                result1.AppendLine(remainingContent);

                AI_Game_Output item1 = AI_Game_Output.getByid("10001");
                string str1 = await GenerateGameBaseJson(item1.jsonModle, newresult.ToString(), "GameBase");
                AI_Game_Output item2 = AI_Game_Output.getByid("10002");
                string str2 = await GenerateGameBaseJson(item2.jsonModle, result1.ToString(), "worldviewandtarget");
                outstr = str1 + str2;
            }
            return outstr;

        }
        public async Task GetIGNreviewlink(Action<string> callback)
        {
            AI_Game_Output item = AI_Game_Output.getByid("10004");
            if (item != null)
            {
                if (item.ifParallel == true)
                {
                    List<AI_Game_Output> alllist = AI_Game_Output.getAllDataToList();

                    // 筛选出所有ifJson值与concurrency相同的数据
                    List<AI_Game_Output> filteredList = alllist.Where(x => x.step == item.step).ToList();

                    // 输出筛选结果
                    Console.WriteLine($"找到 {filteredList.Count} 条step为{item.step}的记录:");
                    string gamebase = Path.Combine("baseTemplate", "GameBase.json");
                    string str1 = File.ReadAllText(gamebase);
                    string worldview = Path.Combine("baseTemplate", "worldviewandtarget.json");
                    string str2 = File.ReadAllText(worldview);
                    foreach (var output in filteredList)
                    {
                        //Console.WriteLine($"ID: {output.id}, 描述: {output.templateDescription}");
                        string prompt = "";
                        switch (output.id)
                        {
                            case "10004":
                            case "10005":
                                prompt = str1 + str2;
                                break;
                            case "10006":
                                prompt = str2;
                                break;
                        }
                        TaskData data = new TaskData();
                        data.id = output.id + "-" + GenerateUUid();
                        data.systemPrompt = output.systemPrompt;
                        data.prompt = prompt;
                        data.baseid = output.id;
                        taskList.Add(data);
                    }
                }
                else
                {
                    //string priority = item.priority;
                    //string jsonModle = item.jsonModle;
                    //string systemprompt = item.systemPrompt;
                    //await ChatWithAI(systemprompt, jsonString, "worldviewandtarget", priority, callback, jsonModle);
                }
                if (taskList.Count > 0)
                {
                    await AddNewConversation(callback);
                }
            }

        }
        // 暂停特定对话

        public void PauseConversation(string id)
        {
            if (_conversationStatuses.TryGetValue(id, out var status) && status == ConversationStatus.Running)
            {
                if (_pauseSemaphores.TryGetValue(id, out var semaphore))
                {
                    // 设置信号量为0，使对话暂停
                    while (semaphore.CurrentCount > 0)
                    {
                        semaphore.Wait(0);
                    }

                    _conversationStatuses[id] = ConversationStatus.Paused;
                    OnChatStatusChanged(new ChatStatusChangedEventArgs { Id = id, Status = ConversationStatus.Paused });
                    Console.WriteLine($"对话 {id} 已暂停");
                }
            }
            else
            {
                Console.WriteLine($"对话 {id} 无法暂停，当前状态: {status}");
            }
        }
        // 恢复特定对话
        public void ResumeConversation(string id)
        {
            if (_conversationStatuses.TryGetValue(id, out var status) && status == ConversationStatus.Paused)
            {
                if (_pauseSemaphores.TryGetValue(id, out var semaphore))
                {
                    // 释放信号量，使对话继续
                    semaphore.Release();

                    _conversationStatuses[id] = ConversationStatus.Running;
                    OnChatStatusChanged(new ChatStatusChangedEventArgs { Id = id, Status = ConversationStatus.Running });
                    Console.WriteLine($"对话 {id} 已恢复");
                }
            }
            else
            {
                Console.WriteLine($"对话 {id} 无法恢复，当前状态: {status}");
            }
        }

        // 取消特定对话
        public void CancelConversation(string id)
        {
            if (_cancellationSources.TryGetValue(id, out var cts))
            {
                cts.Cancel();
                _conversationStatuses[id] = ConversationStatus.Cancelled;
                OnChatStatusChanged(new ChatStatusChangedEventArgs { Id = id, Status = ConversationStatus.Cancelled });
                Console.WriteLine($"对话 {id} 已取消");
            }
            else
            {
                Console.WriteLine($"对话 {id} 无法取消，未找到相关信息");
            }
        }
        //// 显示所有对话状态
        //public void ShowAllConversationStatus()
        //{
        //    Console.WriteLine("\n=== 对话状态 ===");
        //    foreach (var status in _conversationStatuses)
        //    {
        //        Console.WriteLine($"对话 {status.Key}: {status.Value}");
        //    }
        //    Console.WriteLine("================");
        //}
        private void testAddTask()
        {
            TaskData task1 = new TaskData();
            task1.id = "1";
            task1.prompt = "如何学习C#编程";
            TaskData task2 = new TaskData();
            task2.id = "1";
            task2.prompt = "用简单的语言说明相对论";
            TaskData task3 = new TaskData();
            task3.id = "1";
            task3.prompt = "解释一下最新的八项原则";
            taskList.Add(task1);
            taskList.Add(task2);
            taskList.Add(task3);
        }

        // 动态添加新对话
        public async Task AddNewConversation(Action<string> callback)
        {


            //Console.WriteLine($"已添加新对话，ID: {data.id}");
            //// 启动新对话
            //await Task.Run(() => StreamConversationAsync(data.id, data.systemPrompt ,data.prompt, cts.Token, pauseSemaphore));

            // 启动所有流式对话，但不等待它们完成
            var streamingTasks = new List<Task>();
            foreach (var conv in taskList)
            {
                // 为新对话创建取消令牌源和暂停信号量
                var cts = new CancellationTokenSource();
                var pauseSemaphore = new SemaphoreSlim(1, 1);

                _cancellationSources[conv.id] = cts;
                _pauseSemaphores[conv.id] = pauseSemaphore;
                _conversationStatuses[conv.id] = ConversationStatus.NotStarted;

                // 使用Task.Run确保对话在独立线程中运行
                streamingTasks.Add(Task.Run(() => StreamConversationAsync(conv.id, conv.baseid, conv.systemPrompt, conv.prompt, cts.Token, pauseSemaphore, callback)));
            }
            try
            {
                await Task.WhenAll(streamingTasks);
                Console.WriteLine("所有对话已完成");
            }
            catch (TaskCanceledException)
            {
                Console.WriteLine("一个或多个对话已取消");
            }
        }

        public async Task RunConcurrentChats(Action<string> callback)
        {
            _completeResponses.Clear();
            // 清理旧的取消令牌和信号量
            foreach (var key in _cancellationSources.Keys)
            {
                if (_cancellationSources.TryRemove(key, out var cts))
                {
                    cts.Dispose();
                }
            }

            foreach (var key in _pauseSemaphores.Keys)
            {
                if (_pauseSemaphores.TryRemove(key, out var semaphore))
                {
                    semaphore.Dispose();
                }
            }

            testAddTask();
            // 启动所有流式对话，但不等待它们完成
            var streamingTasks = new List<Task>();
            foreach (var conv in taskList)
            {
                // 为每个对话创建新的取消令牌源和暂停信号量
                var cts = new CancellationTokenSource();
                var pauseSemaphore = new SemaphoreSlim(1, 1); // 初始值为1，表示不暂停

                _cancellationSources[conv.id] = cts;
                _pauseSemaphores[conv.id] = pauseSemaphore;
                _conversationStatuses[conv.id] = ConversationStatus.NotStarted;

                // 使用Task.Run确保对话在独立线程中运行
                streamingTasks.Add(Task.Run(() => StreamConversationAsync(conv.id, conv.baseid, conv.systemPrompt, conv.prompt, cts.Token, pauseSemaphore, callback)));
            }

            try
            {
                await Task.WhenAll(streamingTasks);
                Console.WriteLine("所有对话已完成");
            }
            catch (TaskCanceledException)
            {
                Console.WriteLine("一个或多个对话已取消");
            }
        }


        private async Task StreamConversationAsync(string id, string baseid, string systemprompt, string prompt, CancellationToken cancellationToken, SemaphoreSlim pauseSemaphore, Action<string> callback)
        {
            try
            {
                _conversationStatuses[id] = ConversationStatus.Running;
                OnChatStatusChanged(new ChatStatusChangedEventArgs { Id = id, Status = ConversationStatus.Running });
                Console.WriteLine($"\n=== 对话 {id} 开始 ===");
                Console.WriteLine($"用户: {prompt}");
                Console.Write($"AI {id}: ");

                var chatHistory = new ChatHistory();
                if (!string.IsNullOrEmpty(systemprompt))
                {
                    chatHistory.AddSystemMessage(systemprompt);
                }
                chatHistory.AddUserMessage(prompt);

                var streamingResponse = _chatService.GetStreamingChatMessageContentsAsync(chatHistory, cancellationToken: cancellationToken);
                StringBuilder result = new StringBuilder();
                await foreach (var chunk in streamingResponse.WithCancellation(cancellationToken))
                {
                    // 检查是否需要暂停
                    await pauseSemaphore.WaitAsync(cancellationToken);
                    try
                    {
                        if (chunk.Content is not null)
                        {
                            Console.Write(chunk.Content);
                            result.Append(chunk.Content);
                        }
                    }
                    finally
                    {
                        pauseSemaphore.Release();
                    }

                    // 模拟处理时间，使暂停更明显
                    await Task.Delay(50, cancellationToken);
                }
                string response = result.ToString();
                _completeResponses[id] = response;

                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"\n=== 对话 {id} 结束 ===");
                Console.ResetColor();

                _conversationStatuses[id] = ConversationStatus.Completed;

                AI_Game_Output item = AI_Game_Output.getByid($"{baseid}");
                string outStr = "";
                if (item != null)
                {
                    string fileName = "";
                    switch (baseid)
                    {
                        case "10001":
                            fileName = "GameBase";
                            break;
                        case "10002":
                            fileName = "worldviewandtarget";
                            break;
                        case "10003":
                            fileName = "CompleteBase";
                            break;
                        case "10004":
                            fileName = "IGNlink";
                            break;
                        case "10005":
                            fileName = "GameIntroduce";
                            break;
                        case "10006":
                            fileName = "Chapter";
                            break;
                        case "10007":
                            fileName = "Character";
                            break;
                        case "10008":
                            fileName = "Scence";
                            break;
                        case "10009":
                            fileName = "ScenceMusic";
                            break;
                        case "10010":
                            fileName = "MusicEffect";
                            break;
                        case "10011":
                            fileName = "SkyBox";
                            break;

                    }
                    switch (item.priority)
                    {
                        case "JSON":
                            outStr = await GenerateGameBaseJson(item.jsonModle, response, fileName);

                            Console.WriteLine($"\n------ 生成 {fileName} .json完成 ------");
                            break;
                        case "MARKDOWN":
                            outStr = await GenerateMDfile(response, fileName);
                            Console.WriteLine($"\n------ 生成 {fileName} .md完成 ------");
                            break;
                    }
                    if (callback != null)
                    {
                        outStr = baseid + "|" + outStr;
                        callback(outStr);
                    }
                    //if (item.priority == "JSON")
                    //{
                    //    string jsonksy = item.jsonModle;
                    //    string outStr = await GenerateGameBaseJson(jsonksy, response, "GameBase");

                    //}

                }
                OnChatStatusChanged(new ChatStatusChangedEventArgs { Id = id, Status = ConversationStatus.Completed });

                OnChatCompleted(new ChatCompletedEventArgs
                {
                    Id = id,
                    BaseId = baseid,
                    Prompt = prompt,
                    Response = response,
                    CallBack = callback,
                });
            }
            catch (OperationCanceledException)
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"\n=== 对话 {id} 已取消 ===");
                Console.ResetColor();

                _conversationStatuses[id] = ConversationStatus.Cancelled;
                OnChatStatusChanged(new ChatStatusChangedEventArgs { Id = id, Status = ConversationStatus.Cancelled });
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"\n=== 对话 {id} 发生错误: {ex.Message} ===");
                Console.ResetColor();
            }

        }
        // 触发对话完成事件
        protected virtual void OnChatCompleted(ChatCompletedEventArgs e)
        {
            ChatCompleted?.Invoke(this, e);
        }
        // 触发状态变更事件
        protected virtual void OnChatStatusChanged(ChatStatusChangedEventArgs e)
        {
            ChatStatusChanged?.Invoke(this, e);
        }
        //随机一个uuid
        private string GenerateUUid()
        {
            Guid uuid = Guid.NewGuid();
            string uuidString = uuid.ToString();
            return uuidString;
        }
        private void ClearTasklist()
        {
            if (taskList != null)
            {
                taskList.Clear();
            }
            else
            {
                taskList = new List<TaskData>();
            }
        }

        public async Task<string> ChatWithAI(string systemprompt, string prompt, string filename, string outType, Action<string> callback, string jsonPrompt = "")
        {
            string outstr = "";
            var chatHistory = new ChatHistory();
            if (!string.IsNullOrEmpty(systemprompt))
            {
                chatHistory.AddSystemMessage(systemprompt);
            }
            chatHistory.AddUserMessage(prompt); // 添加用户输入到历史
            var generatedContent = new StringBuilder();
            await foreach (var chunk in _chatService.GetStreamingChatMessageContentsAsync(chatHistory))
            {
                Console.Write(chunk.Content);
                generatedContent.Append(chunk.Content);
            }
            //outstr = generatedContent.ToString();
            // AI 的回复会自动添加到 chatHistory 中，后续对话可以基于此继续

            if (!Directory.Exists(baseTemplatePath))
                Directory.CreateDirectory(baseTemplatePath);

            string outputPath = Path.Combine(baseTemplatePath, $"{filename}.txt");
            await File.WriteAllTextAsync(outputPath, generatedContent.ToString());
            Console.WriteLine($"\n------ 生成 {filename} .txt完成 ------");
            switch (outType)
            {
                case "JSON":
                    outstr = await GenerateGameBaseJson(jsonPrompt, generatedContent.ToString(), filename);
                    Console.WriteLine($"\n------ 生成 {filename} .json完成 ------");
                    break;
                case "MARKDOWN":
                    outstr = await GenerateMDfile(generatedContent.ToString(), filename);
                    Console.WriteLine($"\n------ 生成 {filename} .json完成 ------");
                    break;
            }
            if (callback != null)
            {
                callback(outstr);
            }
            return outstr;
        }

        /// <summary>
        /// 生成.md文件
        /// </summary>
        /// <param name="chatService"></param>
        /// <param name="prompt"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public async Task<string> GenerateMDfile(string prompt, string filename)
        {
            string mdStr = "";
            AI_Game_Output item = AI_Game_Output.getByid("10011");
            if (item != null)
            {
                string markdownKeys = item.generateTemplate;
                string promptStr = prompt + $"\r\n{markdownKeys}";
                Console.WriteLine($"markdown KEYS : {promptStr}");
                var chatHistory = new ChatHistory();
                chatHistory.AddUserMessage(promptStr); // 添加用户输入到历史
                var generatedContent = new StringBuilder();
                await foreach (var chunk in _chatService.GetStreamingChatMessageContentsAsync(chatHistory))
                {
                    Console.Write(chunk.Content);
                    generatedContent.Append(chunk.Content);
                }
                if (!Directory.Exists(baseTemplatePath))
                    Directory.CreateDirectory(baseTemplatePath);

                mdStr = RemoveMarkdownCodeBlocks(generatedContent.ToString());
                string outputPath = Path.Combine(baseTemplatePath, $"{filename}.md");
                await File.WriteAllTextAsync(outputPath, mdStr.ToString());
            }
            return mdStr;
        }

        public async Task<string> GenerateGameBaseJson(string systemprompt, string prompt, string filename)
        {

            var chatHistory = new ChatHistory();
            if (!string.IsNullOrEmpty(systemprompt))
            {
                chatHistory.AddSystemMessage(systemprompt);
            }
            chatHistory.AddUserMessage(prompt); // 添加用户输入到历史
            var generatedContent = new StringBuilder();
            await foreach (var chunk in _chatService.GetStreamingChatMessageContentsAsync(chatHistory))
            {
                Console.Write(chunk.Content);
                generatedContent.Append(chunk.Content);
            }
            // AI 的回复会自动添加到 chatHistory 中，后续对话可以基于此继续

            if (!Directory.Exists(baseTemplatePath))
                Directory.CreateDirectory(baseTemplatePath);

            string mdStr = RemoveJSONCodeBlocks(generatedContent.ToString());
            string outputPath = Path.Combine(baseTemplatePath, $"{filename}.json");
            await File.WriteAllTextAsync(outputPath, mdStr.ToString());
            return mdStr;
        }
        /// <summary>
        /// 去除 ```json  ```
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        string RemoveJSONCodeBlocks(string input)
        {
            // 移除开头的```markdown
            int startIndex = input.IndexOf("```json");
            if (startIndex >= 0)
            {
                input = input.Substring(startIndex + "```json".Length);
            }

            // 移除结尾的```
            int endIndex = input.LastIndexOf("```");
            if (endIndex >= 0)
            {
                input = input.Substring(0, endIndex);
            }

            return input.Trim();
        }
        /// <summary>
        /// 去除 ```markdown  ```
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        string RemoveMarkdownCodeBlocks(string input)
        {
            // 移除开头的```markdown
            int startIndex = input.IndexOf("```markdown");
            if (startIndex >= 0)
            {
                input = input.Substring(startIndex + "```markdown".Length);
            }

            // 移除结尾的```
            int endIndex = input.LastIndexOf("```");
            if (endIndex >= 0)
            {
                input = input.Substring(0, endIndex);
            }

            return input.Trim();
        }
    }
    public class TaskData
    {
        public string id { get; set; }
        public string baseid { get; set; }
        public string systemPrompt { get; set; }
        public string prompt { get; set; }

    }
    public class NewGameBase
    {
        //游戏名
        public string gamename { get; set; }
        //题材
        public string theme { get; set; }
        //游戏类型
        public string gametype { get; set; }
        //游戏视角
        public string perspective { get; set; }
        //2d/3d
        public string dimension { get; set; }

    }
    public class WorldviewTarget
    {
        //世界观
        public string worldview { get; set; }
        //游戏剧情
        public string story { get; set; }
        //游戏目标
        public NewGameTarget objectives { get; set; }
    }
    public class NewGameTarget
    {
        public string short_term { get; set; }
        public string mid_term { get; set; }
        public string long_term { get; set; }
    }

}
