using Microsoft.AspNetCore.Http;
using System;
using System.IO;
using System.Threading.Tasks;

namespace SaveDataService
{
    /// <summary>
    /// Web服务器处理器 - 处理静态文件和其他Web相关请求
    /// </summary>
    public class WebServerHandle
    {
        /// <summary>
        /// 处理静态文件请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="fileName">文件名</param>
        public static async Task HandleStaticFile(HttpContext context, string fileName)
        {
            try
            {
                var filePath = Path.Combine(AppContext.BaseDirectory, fileName);

                if (File.Exists(filePath))
                {
                    context.Response.ContentType = GetContentType(fileName);
                    var fileContent = await File.ReadAllTextAsync(filePath);
                    await context.Response.WriteAsync(fileContent);
                }
                else
                {
                    context.Response.StatusCode = 404;
                    context.Response.ContentType = "text/plain";
                    await context.Response.WriteAsync("File not found");
                }
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = 500;
                context.Response.ContentType = "text/plain";
                await context.Response.WriteAsync($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取文件的 Content-Type
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>对应的 Content-Type</returns>
        public static string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".html" => "text/html; charset=utf-8",
                ".css" => "text/css; charset=utf-8",
                ".js" => "application/javascript; charset=utf-8",
                ".json" => "application/json; charset=utf-8",
                ".png" => "image/png",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".gif" => "image/gif",
                ".svg" => "image/svg+xml",
                _ => "text/plain; charset=utf-8"
            };
        }
    }
}
