using Microsoft.AspNetCore.Http;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;
using System.Collections.Concurrent;

namespace SaveDataService
{
    /// <summary>
    /// 文件上传信息
    /// </summary>
    public class FileUploadInfo
    {
        public string FileName { get; set; } = "";
        public long TotalSize { get; set; }
        public long UploadedSize { get; set; }
        public string FileHash { get; set; } = "";
        public DateTime LastModified { get; set; }
        public string TempFilePath { get; set; } = "";
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// 文件上传响应
    /// </summary>
    public class FileUploadResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public long UploadedSize { get; set; }
        public long TotalSize { get; set; }
        public double Progress { get; set; }
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// 文件移动请求
    /// </summary>
    public class MoveFileRequest
    {
        public string SourcePath { get; set; } = "";
        public string TargetPath { get; set; } = "";
    }

    /// <summary>
    /// Web服务器处理器 - 处理静态文件和其他Web相关请求
    /// </summary>
    public class WebServerHandle
    {
        // 存储上传中的文件信息
        private static readonly ConcurrentDictionary<string, FileUploadInfo> _uploadSessions = new();

        // 上传文件存储目录
        private static readonly string _uploadDirectory = Path.Combine(AppContext.BaseDirectory, "uploads");

        // 临时文件存储目录
        private static readonly string _tempDirectory = Path.Combine(AppContext.BaseDirectory, "temp");

        static WebServerHandle()
        {
            // 确保上传目录存在
            Directory.CreateDirectory(_uploadDirectory);
            Directory.CreateDirectory(_tempDirectory);
        }
        /// <summary>
        /// 处理静态文件请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="fileName">文件名</param>
        public static async Task HandleStaticFile(HttpContext context, string fileName)
        {
            try
            {
                var filePath = Path.Combine(AppContext.BaseDirectory, fileName);

                // 调试信息
                Console.WriteLine($"[DEBUG] 请求文件: {fileName}");
                Console.WriteLine($"[DEBUG] 完整路径: {filePath}");
                Console.WriteLine($"[DEBUG] 文件是否存在: {File.Exists(filePath)}");

                if (File.Exists(filePath))
                {
                    context.Response.ContentType = GetContentType(fileName);
                    Console.WriteLine($"[DEBUG] Content-Type: {context.Response.ContentType}");

                    // 对于文本文件，使用UTF-8编码读取
                    var extension = Path.GetExtension(fileName).ToLower();
                    if (extension == ".html" || extension == ".css" || extension == ".js" || extension == ".json")
                    {
                        var fileContent = await File.ReadAllTextAsync(filePath, System.Text.Encoding.UTF8);
                        Console.WriteLine($"[DEBUG] 文件内容长度: {fileContent.Length} 字符");
                        Console.WriteLine($"[DEBUG] 文件内容前100字符: {fileContent.Substring(0, Math.Min(100, fileContent.Length))}");
                        await context.Response.WriteAsync(fileContent, System.Text.Encoding.UTF8);
                    }
                    else
                    {
                        // 对于二进制文件，直接传输字节
                        var fileBytes = await File.ReadAllBytesAsync(filePath);
                        Console.WriteLine($"[DEBUG] 文件大小: {fileBytes.Length} 字节");
                        await context.Response.Body.WriteAsync(fileBytes, 0, fileBytes.Length);
                    }
                    Console.WriteLine($"[DEBUG] 文件发送完成");
                }
                else
                {
                    Console.WriteLine($"[DEBUG] 文件不存在，返回404");
                    context.Response.StatusCode = 404;
                    context.Response.ContentType = "text/plain; charset=utf-8";
                    await context.Response.WriteAsync($"File not found: {filePath}", System.Text.Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 处理静态文件时出错: {ex.Message}");
                Console.WriteLine($"[ERROR] 堆栈跟踪: {ex.StackTrace}");
                context.Response.StatusCode = 500;
                context.Response.ContentType = "text/plain; charset=utf-8";
                await context.Response.WriteAsync($"Error: {ex.Message}", System.Text.Encoding.UTF8);
            }
        }

        /// <summary>
        /// 处理文件上传请求（支持断点续传）
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public static async Task HandleFileUpload(HttpContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                // 设置CORS头
                response.Headers.Add("Access-Control-Allow-Origin", "*");
                response.Headers.Add("Access-Control-Allow-Methods", "POST, OPTIONS");
                response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Content-Range, X-File-Name, X-File-Size, X-File-Hash");

                if (request.Method == "OPTIONS")
                {
                    response.StatusCode = 200;
                    return;
                }

                if (request.Method != "POST")
                {
                    response.StatusCode = 405;
                    await response.WriteAsync("Method not allowed");
                    return;
                }

                // 获取文件信息
                var fileName = request.Headers["X-File-Name"].FirstOrDefault() ?? "unknown";
                var fileSizeStr = request.Headers["X-File-Size"].FirstOrDefault();
                var fileHash = request.Headers["X-File-Hash"].FirstOrDefault() ?? "";
                var contentRange = request.Headers["Content-Range"].FirstOrDefault();

                if (!long.TryParse(fileSizeStr, out long totalSize))
                {
                    await SendErrorResponse(response, "Invalid file size");
                    return;
                }

                // 解析Content-Range头
                long startByte = 0;
                long endByte = totalSize - 1;

                if (!string.IsNullOrEmpty(contentRange))
                {
                    // 格式: bytes 0-1023/2048
                    var parts = contentRange.Replace("bytes ", "").Split('/');
                    if (parts.Length == 2)
                    {
                        var range = parts[0].Split('-');
                        if (range.Length == 2)
                        {
                            if (long.TryParse(range[0], out var start))
                                startByte = start;
                            if (long.TryParse(range[1], out var end))
                                endByte = end;
                        }
                    }
                }

                var sessionId = $"{fileName}_{fileHash}";
                var uploadInfo = _uploadSessions.GetOrAdd(sessionId, _ => new FileUploadInfo
                {
                    FileName = fileName,
                    TotalSize = totalSize,
                    FileHash = fileHash,
                    TempFilePath = Path.Combine(_tempDirectory, $"{sessionId}.tmp"),
                    LastModified = DateTime.Now
                });

                // 写入文件数据
                var chunkSize = endByte - startByte + 1;
                var buffer = new byte[chunkSize];
                var bytesRead = await request.Body.ReadAsync(buffer, 0, (int)chunkSize);

                await WriteFileChunk(uploadInfo, buffer, startByte, bytesRead);

                uploadInfo.UploadedSize = Math.Max(uploadInfo.UploadedSize, endByte + 1);
                uploadInfo.LastModified = DateTime.Now;

                // 检查是否上传完成
                if (uploadInfo.UploadedSize >= uploadInfo.TotalSize)
                {
                    await CompleteFileUpload(uploadInfo);
                    _uploadSessions.TryRemove(sessionId, out _);
                }

                // 返回上传状态
                var uploadResponse = new FileUploadResponse
                {
                    Success = true,
                    Message = "Chunk uploaded successfully",
                    UploadedSize = uploadInfo.UploadedSize,
                    TotalSize = uploadInfo.TotalSize,
                    Progress = (double)uploadInfo.UploadedSize / uploadInfo.TotalSize * 100,
                    IsCompleted = uploadInfo.IsCompleted
                };

                response.ContentType = "application/json";
                await response.WriteAsync(JsonSerializer.Serialize(uploadResponse));
            }
            catch (Exception ex)
            {
                await SendErrorResponse(context.Response, $"Upload error: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入文件块
        /// </summary>
        private static async Task WriteFileChunk(FileUploadInfo uploadInfo, byte[] data, long offset, int length)
        {
            using var fileStream = new FileStream(uploadInfo.TempFilePath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read);
            fileStream.Seek(offset, SeekOrigin.Begin);
            await fileStream.WriteAsync(data, 0, length);
            await fileStream.FlushAsync();
        }

        /// <summary>
        /// 完成文件上传
        /// </summary>
        private static Task CompleteFileUpload(FileUploadInfo uploadInfo)
        {
            var finalPath = Path.Combine(_uploadDirectory, uploadInfo.FileName);

            // 确保目标目录存在
            var directory = Path.GetDirectoryName(finalPath);
            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 移动临时文件到最终位置
            if (File.Exists(finalPath))
            {
                File.Delete(finalPath);
            }

            File.Move(uploadInfo.TempFilePath, finalPath);
            uploadInfo.IsCompleted = true;

            Console.WriteLine($"文件上传完成: {uploadInfo.FileName}");
            return Task.CompletedTask;
        }

        /// <summary>
        /// 发送错误响应
        /// </summary>
        private static async Task SendErrorResponse(HttpResponse response, string message)
        {
            response.StatusCode = 400;
            response.ContentType = "application/json";
            var errorResponse = new FileUploadResponse
            {
                Success = false,
                Message = message
            };
            await response.WriteAsync(JsonSerializer.Serialize(errorResponse));
        }

        /// <summary>
        /// 处理HTML文件移动请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public static async Task HandleMoveHtmlFile(HttpContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                // 设置CORS头
                response.Headers.Add("Access-Control-Allow-Origin", "*");
                response.Headers.Add("Access-Control-Allow-Methods", "POST, OPTIONS");
                response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

                if (request.Method == "OPTIONS")
                {
                    response.StatusCode = 200;
                    return;
                }

                if (request.Method != "POST")
                {
                    response.StatusCode = 405;
                    await response.WriteAsync("Method not allowed");
                    return;
                }

                // 读取请求体
                using var reader = new StreamReader(request.Body);
                var requestBody = await reader.ReadToEndAsync();
                var moveRequest = JsonSerializer.Deserialize<MoveFileRequest>(requestBody);

                if (moveRequest == null || string.IsNullOrEmpty(moveRequest.SourcePath) || string.IsNullOrEmpty(moveRequest.TargetPath))
                {
                    await SendErrorResponse(response, "Invalid move request");
                    return;
                }

                var sourcePath = Path.Combine(AppContext.BaseDirectory, moveRequest.SourcePath);
                var targetPath = Path.Combine(AppContext.BaseDirectory, moveRequest.TargetPath);

                if (!File.Exists(sourcePath))
                {
                    await SendErrorResponse(response, "Source file not found");
                    return;
                }

                // 确保目标目录存在
                var targetDirectory = Path.GetDirectoryName(targetPath);
                if (!string.IsNullOrEmpty(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                // 移动文件
                if (File.Exists(targetPath))
                {
                    File.Delete(targetPath);
                }
                File.Move(sourcePath, targetPath);

                var moveResponse = new { Success = true, Message = "File moved successfully" };
                response.ContentType = "application/json";
                await response.WriteAsync(JsonSerializer.Serialize(moveResponse));
            }
            catch (Exception ex)
            {
                await SendErrorResponse(context.Response, $"Move error: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理文件夹批量上传请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public static async Task HandleFolderUpload(HttpContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                // 设置CORS头
                response.Headers.Add("Access-Control-Allow-Origin", "*");
                response.Headers.Add("Access-Control-Allow-Methods", "POST, OPTIONS");
                response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, X-Folder-Path");

                if (request.Method == "OPTIONS")
                {
                    response.StatusCode = 200;
                    return;
                }

                if (request.Method != "POST")
                {
                    response.StatusCode = 405;
                    await response.WriteAsync("Method not allowed");
                    return;
                }

                var folderPath = request.Headers["X-Folder-Path"].FirstOrDefault() ?? "uploads";
                var targetDirectory = Path.Combine(_uploadDirectory, folderPath);
                Directory.CreateDirectory(targetDirectory);

                // 处理multipart/form-data
                if (request.HasFormContentType && request.Form.Files.Count > 0)
                {
                    var uploadResults = new List<object>();

                    foreach (var file in request.Form.Files)
                    {
                        try
                        {
                            var fileName = file.FileName;
                            var filePath = Path.Combine(targetDirectory, fileName);

                            // 确保子目录存在
                            var fileDirectory = Path.GetDirectoryName(filePath);
                            if (!string.IsNullOrEmpty(fileDirectory))
                            {
                                Directory.CreateDirectory(fileDirectory);
                            }

                            using var fileStream = new FileStream(filePath, FileMode.Create);
                            await file.CopyToAsync(fileStream);

                            uploadResults.Add(new { FileName = fileName, Success = true, Message = "Uploaded successfully" });
                        }
                        catch (Exception ex)
                        {
                            uploadResults.Add(new { FileName = file.FileName, Success = false, Message = ex.Message });
                        }
                    }

                    var folderResponse = new { Success = true, Message = "Folder upload completed", Results = uploadResults };
                    response.ContentType = "application/json";
                    await response.WriteAsync(JsonSerializer.Serialize(folderResponse));
                }
                else
                {
                    await SendErrorResponse(response, "No files found in request");
                }
            }
            catch (Exception ex)
            {
                await SendErrorResponse(context.Response, $"Folder upload error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取上传进度
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public static async Task HandleGetUploadProgress(HttpContext context)
        {
            try
            {
                var sessionId = context.Request.Query["sessionId"].FirstOrDefault();
                if (string.IsNullOrEmpty(sessionId))
                {
                    await SendErrorResponse(context.Response, "Session ID required");
                    return;
                }

                if (_uploadSessions.TryGetValue(sessionId, out var uploadInfo))
                {
                    var progressResponse = new FileUploadResponse
                    {
                        Success = true,
                        Message = "Progress retrieved",
                        UploadedSize = uploadInfo.UploadedSize,
                        TotalSize = uploadInfo.TotalSize,
                        Progress = (double)uploadInfo.UploadedSize / uploadInfo.TotalSize * 100,
                        IsCompleted = uploadInfo.IsCompleted
                    };

                    context.Response.ContentType = "application/json";
                    await context.Response.WriteAsync(JsonSerializer.Serialize(progressResponse));
                }
                else
                {
                    await SendErrorResponse(context.Response, "Upload session not found");
                }
            }
            catch (Exception ex)
            {
                await SendErrorResponse(context.Response, $"Progress error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取文件的 Content-Type
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>对应的 Content-Type</returns>
        public static string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".html" => "text/html; charset=utf-8",
                ".css" => "text/css; charset=utf-8",
                ".js" => "application/javascript; charset=utf-8",
                ".json" => "application/json; charset=utf-8",
                ".png" => "image/png",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".gif" => "image/gif",
                ".svg" => "image/svg+xml",
                _ => "text/plain; charset=utf-8"
            };
        }
    }
}
