using System.Security.Cryptography.X509Certificates;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SaveDataService;
using System.Text;
using Newtonsoft.Json;

public class KestrelServerSetup
{
    /// <summary>
    /// 配置并启动 Kestrel 服务器
    /// </summary>
    /// <param name="hostName">主机名</param>
    /// <param name="httpPort">HTTP 端口</param>
    /// <param name="httpsPort">HTTPS 端口</param>
    /// <param name="certificatePath">证书文件路径</param>
    /// <param name="certificatePassword">证书密码</param>
    public void ConfigureAndStartKestrel(string hostName, int httpPort, int httpsPort, string certificatePath, string certificatePassword)
    {
        // 创建 WebHostBuilder 实例
        var hostBuilder = new WebHostBuilder()
            // 使用 Kestrel 作为 web 服务器
            .UseKestrel(options =>
            {
                // 配置 HTTP 终结点
                ConfigureHttpEndpoint(options, hostName, httpPort);

                // 只有在提供了证书时才配置 HTTPS 终结点
                if (!string.IsNullOrEmpty(certificatePath) && !string.IsNullOrEmpty(certificatePassword))
                {
                    ConfigureHttpsEndpoint(options, hostName, httpsPort, certificatePath, certificatePassword);
                }

                // 配置 Kestrel 服务器选项
                ConfigureKestrelServerOptions(options);
            })
            // 配置应用程序服务
            .ConfigureServices(services =>
            {
                // 添加响应压缩服务
                AddResponseCompression(services);
            })
            // 配置应用程序中间件
            .Configure(app =>
            {
                // 启用响应压缩中间件
                app.UseResponseCompression();

                // 配置 WebSocket 中间件
                ConfigureWebSocketMiddleware(app);

                // 配置默认请求处理
                ConfigureDefaultRequestHandling(app);
            });

        // 构建并运行主机
        hostBuilder.Build().Run();
    }

    /// <summary>
    /// 配置 HTTP 终结点
    /// </summary>
    /// <param name="options">Kestrel 服务器选项</param>
    /// <param name="hostName">主机名</param>
    /// <param name="port">端口号</param>
    private void ConfigureHttpEndpoint(KestrelServerOptions options, string hostName, int port)
    {
        // 监听指定的 HTTP 端口
        options.ListenAnyIP(port, listenOptions =>
        {
            // HTTP 端点，不使用 HTTPS
            Console.WriteLine($"HTTP 服务器监听端口: {port}");
        });
    }

    /// <summary>
    /// 配置 HTTPS 终结点
    /// </summary>
    /// <param name="options">Kestrel 服务器选项</param>
    /// <param name="hostName">主机名</param>
    /// <param name="port">端口号</param>
    /// <param name="certificatePath">证书文件路径</param>
    /// <param name="certificatePassword">证书密码</param>
    private void ConfigureHttpsEndpoint(KestrelServerOptions options, string hostName, int port, string certificatePath, string certificatePassword)
    {
        // 从文件加载 X.509 证书
        var certificate = new X509Certificate2(certificatePath, certificatePassword);

        // 监听指定的 HTTPS 端口
        options.ListenAnyIP(port, listenOptions =>
        {


            // 配置 HTTPS 使用加载的证书
            listenOptions.UseHttps(certificate);

            // 启用 HTTP/3 支持（可选，需要 .NET 7+ 和操作系统支持）
            // listenOptions.Protocols = HttpProtocols.Http1AndHttp2AndHttp3;
        });
    }

    /// <summary>
    /// 配置 Kestrel 服务器选项
    /// </summary>
    /// <param name="options">Kestrel 服务器选项</param>
    private void ConfigureKestrelServerOptions(KestrelServerOptions options)
    {
        // 配置所有终结点默认限制
        options.Limits.MaxConcurrentConnections = 100;          // 最大并发连接数
        options.Limits.MaxConcurrentUpgradedConnections = 100;  // 最大升级连接数（如 WebSocket）
        options.Limits.MaxRequestBodySize = 10 * 1024 * 1024;   // 最大请求体大小（10MB）
        options.Limits.MinRequestBodyDataRate = null;          // 禁用请求体最小数据速率限制
    }

    /// <summary>
    /// 添加响应压缩服务
    /// </summary>
    /// <param name="services">服务集合</param>
    private void AddResponseCompression(IServiceCollection services)
    {
        // 添加响应压缩服务到依赖注入容器
        services.AddResponseCompression(options =>
        {
            // 启用对 HTTPS 的响应压缩
            options.EnableForHttps = true;

            // 配置压缩提供程序

            options.Providers.Add<GzipCompressionProvider>();

            // 配置需要压缩的 MIME 类型
            options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[]
            {
                "application/xml",
                "image/svg+xml"
            });
        });
    }

    /// <summary>
    /// 配置 WebSocket 中间件
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    private void ConfigureWebSocketMiddleware(IApplicationBuilder app)
    {
        // 启用 WebSocket 中间件
        app.UseWebSockets(new WebSocketOptions
        {
            // 配置 WebSocket 保持活动状态间隔（秒）
            KeepAliveInterval = TimeSpan.FromSeconds(120),

            // 配置接收缓冲区大小
            ReceiveBufferSize = 4 * 1024
        });
    }

    /// <summary>
    /// 配置默认请求处理
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    private void ConfigureDefaultRequestHandling(IApplicationBuilder app)
    {
        // 配置默认请求处理中间件
        app.Run(async context =>
        {
            // 设置响应内容类型为纯文本
            context.Response.ContentType = "text/plain";


            // 检查是否是WebSocket请求
            if (context.WebSockets.IsWebSocketRequest)
            {
                //_logger.LogInformation("收到WebSocket连接请求");
                await HandleWebSocketAsync(context);
            }
            else
            {
                //_logger.LogInformation("收到HTTP请求");
                await HandleHttpRequestAsync(context);
            }






        });
    }
    private async Task HandleWebSocketAsync(HttpContext context)
    {
        using var webSocket = await context.WebSockets.AcceptWebSocketAsync();
        //_logger.LogInformation("WebSocket连接已建立");

        // 简单的回显服务示例
        var buffer = new byte[1024 * 4];
        var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);

        while (!result.CloseStatus.HasValue)
        {
            // 将收到的消息原样发回
            await webSocket.SendAsync(
                new ArraySegment<byte>(buffer, 0, result.Count),
                result.MessageType,
                result.EndOfMessage,
                CancellationToken.None);

            result = await webSocket.ReceiveAsync(
                new ArraySegment<byte>(buffer), CancellationToken.None);
        }

        await webSocket.CloseAsync(
            result.CloseStatus.Value,
            result.CloseStatusDescription,
            CancellationToken.None);
    }

    private async Task HandleHttpRequestAsync(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower() ?? "";
        var method = context.Request.Method.ToUpper();

        // 调试信息
        Console.WriteLine($"[DEBUG] 收到请求: {method} {path}");
        Console.WriteLine($"[DEBUG] 原始路径: {context.Request.Path.Value}");

        // 处理 RESTful API 请求
        if (path == "/getrestful" && method == "GET")
        {
            await HandleGetRestfulApi(context);
            return;
        }

        // 处理静态文件请求
        if (path == "/api-test.html" || path == "/")
        {
            await WebServerHandle.HandleStaticFile(context, "api-test.html");
            return;
        }

        // 处理账号管理 API 请求
        if (path.StartsWith("/api/account/") && method == "POST")
        {
            await AccountApiHandler.HandleAccountApi(context);
            return;
        }

        // 处理文件上传请求（支持断点续传）
        if (path == "/api/upload" && (method == "POST" || method == "OPTIONS"))
        {
            await WebServerHandle.HandleFileUpload(context);
            return;
        }

        // 处理文件夹批量上传请求
        if (path == "/api/upload/folder" && (method == "POST" || method == "OPTIONS"))
        {
            await WebServerHandle.HandleFolderUpload(context);
            return;
        }

        // 处理HTML文件移动请求
        if (path == "/api/move" && (method == "POST" || method == "OPTIONS"))
        {
            await WebServerHandle.HandleMoveHtmlFile(context);
            return;
        }

        // 处理上传进度查询
        if (path == "/api/upload/progress" && method == "GET")
        {
            await WebServerHandle.HandleGetUploadProgress(context);
            return;
        }

        // 处理文件上传页面请求
        if (path == "/upload.html" || path == "/upload")
        {
            Console.WriteLine($"[DEBUG] 匹配到上传页面路由: {path}");
            await WebServerHandle.HandleStaticFile(context, "upload.html");
            return;
        }

        // 处理测试页面请求
        if (path == "/test.html" || path == "/test")
        {
            Console.WriteLine($"[DEBUG] 匹配到测试页面路由: {path}");
            await WebServerHandle.HandleStaticFile(context, "test.html");
            return;
        }

        // 默认响应
        context.Response.ContentType = "text/plain";
        if (context.Request.IsHttps)
        {
            await context.Response.WriteAsync($"Welcome to the secure server! Protocol: {context.Request.Protocol}");
        }
        else
        {
            await context.Response.WriteAsync($"Welcome to the server! Protocol: {context.Request.Protocol}");
        }
    }

    /// <summary>
    /// 处理获取 RESTful API 描述的请求
    /// </summary>
    private async Task HandleGetRestfulApi(HttpContext context)
    {
        try
        {
            // 设置 CORS 头
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            context.Response.ContentType = "application/json; charset=utf-8";

            var apiDescription = AccountManageAPI.GetHttpPostFunction();
            await context.Response.WriteAsync(apiDescription);
        }
        catch (Exception ex)
        {
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json; charset=utf-8";
            var errorResponse = JsonConvert.SerializeObject(new { error = ex.Message });
            await context.Response.WriteAsync(errorResponse);
        }
    }
}