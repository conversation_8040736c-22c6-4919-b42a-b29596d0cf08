﻿using Aliyun.OSS;
using Azure;
using ExcelDataReader.Log;
using ExcelToData;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Net.Http.Headers;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Spreadsheet;
using SaveDataService.Manage;
using SaveDataService.Server.Websocket;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;
using static SaveDataService.Manage.AiConversation;
using k8s;
using static AISDKWebSocket.HttpManager;
using NPOI.HPSF;
using System.Text.RegularExpressions;
using System.Reflection;
using System.IO;
using SaveDataService;
internal class NewPostManager
{
    // 添加一个字典来存储conversation
    private static readonly ConcurrentDictionary<string, AiConversation> _conversationDic = new ConcurrentDictionary<string, AiConversation>();

    static NewPostManager()
    {
        // 创建一个定时器，每小时检查一次过期对话
        //_cleanupTimer = new Timer(CleanupExpiredConversations, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
    }

    public static void init()
    {
        var httpserver = new WebsocketService();
        // 添加新的 DeepSeek 聊天 API 端点
        httpserver.SetHttpAction("/chat", onDeepSeekChat);
        //// 添加获取对话内容的端点
        httpserver.SetHttpAction("/conversation", onGetConversation);
        // 添加获取活跃对话列表的端点
        httpserver.SetHttpAction("/active-conversations", onGetActiveConversations);
        //通过对话id，取消对话内容的生成
        httpserver.SetHttpAction("/cancel-conversation", onCancelConversation);

        // 添加多对话端点
        httpserver.SetHttpAction("/multi-chat", onMultiChat);

        // 添加 RESTful API 相关端点
        httpserver.SetHttpAction("/getrestful", onGetRestfulApi);
        httpserver.SetHttpAction("/api-test.html", onGetApiTestPage);
        httpserver.SetHttpAction("/", onGetApiTestPage); // 根路径也指向测试页面

        // 添加账号管理 API 端点（动态处理）
        httpserver.SetHttpAction("/api/account", onAccountApi);

        //
        ////用户输入生成游戏描述
        //httpserver.SetHttpAction("/userinput", onUserInput);

        Console.WriteLine("HTTP 服务器启动在端口 7778");
        Console.WriteLine("访问地址: http://127.0.0.1:7778/");
        Console.WriteLine("API 测试页面: http://127.0.0.1:7778/api-test.html");
        Console.WriteLine("RESTful API 描述: http://127.0.0.1:7778/getRESTful");

        httpserver.Start(7778);
    }

    //static async Task onUserInput(HttpContext context)
    //{
    //    if (context.Request.Method == "OPTIONS")
    //    {
    //        context.Response.StatusCode = 200;
    //        context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
    //        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
    //        context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
    //        await context.Response.WriteAsync("");
    //    }
    //    else if (context.Request.Method == "POST")
    //    {
    //        string[] contentType = context.Request.ContentType.Split(';');
    //        if (contentType[0] == "application/json" || contentType[0] == "application/x-www-form-urlencoded")
    //        {
    //            int filesize = (int)context.Request.ContentLength;
    //            NewRequestBackInfo backInfo = null;
    //            using (var file = new MemoryStream())
    //            {
    //                int peekpos = 0;
    //                byte[] buf = new byte[4096];
    //                while (peekpos < filesize)
    //                {
    //                    try
    //                    {
    //                        int read = await context.Request.Body.ReadAsync(buf, 0, buf.Length);
    //                        peekpos += read;
    //                        if (read > 0)
    //                        {
    //                            await file.WriteAsync(buf, 0, read);
    //                        }
    //                    }
    //                    catch (Exception err)
    //                    {
    //                        context.Response.StatusCode = 400;
    //                        await context.Response.WriteAsync(err.Message);
    //                        return;
    //                    }
    //                }
    //                string json = System.Text.Encoding.UTF8.GetString(file.ToArray());

    //                NewRequestInfoData RequestInfo = JsonConvert.DeserializeObject<NewRequestInfoData>(json);
    //                if (!string.IsNullOrEmpty(RequestInfo.AiGameOutputId))
    //                {
    //                    AI_Game_Output item = AI_Game_Output.getDataById(RequestInfo.AiGameOutputId);
    //                    if (item != null)
    //                    {

    //                        // 设置响应头
    //                        context.Response.StatusCode = 200;
    //                        context.Response.ContentType = "text/event-stream; charset=utf-8";
    //                        context.Response.Headers.Add("Cache-Control", "no-cache");
    //                        context.Response.Headers.Add("Connection", "keep-alive");
    //                        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

    //                        List<AI_Game_Output> alllist = AI_Game_Output.getAllDatasList();

    //                        // 筛选出所有ifJson值与concurrency相同的数据
    //                        List<AI_Game_Output> filteredList = alllist.Where(x => x.step == item.step).ToList();



    //                        // 输出筛选结果
    //                        Console.WriteLine($"找到 {filteredList.Count} 条step为{item.step}的记录:");
    //                        Dictionary<string, string> dic = new Dictionary<string, string>();
    //                        for (var i = 0; i < filteredList.Count; i++)
    //                        {
    //                            AI_Game_Output data = filteredList[i];
    //                            switch (data.id)
    //                            {
    //                                case "10001":
    //                                    dic.Add(RequestInfo.userInputStr, data.systemPrompt);
    //                                    break;
    //                                case "10002":
    //                                    string outputPath = Path.Combine("baseTemplate", "GameBase.json");
    //                                    string jsonString = File.ReadAllText(outputPath);
    //                                    dic.Add(jsonString, data.systemPrompt);
    //                                    break;
    //                                case "10003":

    //                                    break;
    //                            }

    //                        }
    //                        // 创建多个对话
    //                        List<AiConversation> conversations = await AiConversation.NewCreateMultipleConversations(
    //                        context,
    //                        dic
    //                        );
    //                        RequestInfo.systemPrompt = item.systemPrompt;
    //                        string filename = "";
    //                        switch (RequestInfo.AiGameOutputId)
    //                        {
    //                            case "10001":
    //                                filename = "GameBase";
    //                                break;
    //                            case "10002":
    //                                filename = "WorldViewAndTarget";
    //                                break;
    //                        }

    //                        //新建对话
    //                        AiConversation conversation = await CreateOrGetConversation(context, RequestInfo);
    //                        await conversation.GenerateTextContent(RequestInfo.userInputStr, RequestInfo.systemPrompt, filename);
    //                        //Console.WriteLine(txtContent);
    //                        switch (item.priority)
    //                        {
    //                            case "JSON":
    //                                conversation.SystemPrompt = "";
    //                                conversation.UserPrompt = conversation.TxtContent + item.jsonModle;
    //                                await conversation.NewStreamDeepSeekResponseToClient();
    //                                string mdStr = conversation.RemoveJSONCodeBlocks(conversation.ConversationContent.ToString());
    //                                string outputPath = Path.Combine(NewDeepSeekManager.Instance.baseTemplatePath, $"{filename}.json");
    //                                await File.WriteAllTextAsync(outputPath, mdStr.ToString());

    //                                break;
    //                            case "MARKDOWN":
    //                                break;
    //                        }

    //                    }
    //                }
    //                //// 设置响应头
    //                //context.Response.StatusCode = 200;
    //                //context.Response.ContentType = "text/event-stream; charset=utf-8";
    //                //context.Response.Headers.Add("Cache-Control", "no-cache");
    //                //context.Response.Headers.Add("Connection", "keep-alive");
    //                //context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
    //                ////新建对话
    //                //AiConversation conversation = await CreateOrGetConversation(context, RequestInfo);
    //                //switch (processtype)
    //                //{
    //                //    case NewProcessType.DeepSeekChat:
    //                //        await conversation.NewStreamDeepSeekResponseToClient();
    //                //        break;
    //                //}
    //            }
    //        }
    //        else
    //        {
    //            await context.Response.WriteAsync("not support contentType=" + context.Request.ContentType);
    //        }
    //    }
    //    else
    //    {
    //        await context.Response.WriteAsync("error method");
    //    }
    //}


    static async Task onDeepSeekChat(HttpContext context)
    {
        await order(context, NewProcessType.DeepSeekChat);
    }
    static async Task order(HttpContext context, NewProcessType processtype)
    {
        Console.WriteLine("api userlogin");
        //postCount++;
        if (context.Request.Method == "OPTIONS")
        {
            context.Response.StatusCode = 200;
            context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
            await context.Response.WriteAsync("");
        }
        else if (context.Request.Method == "POST")
        {
            string[] contentType = context.Request.ContentType.Split(';');
            if (contentType[0] == "application/json" || contentType[0] == "application/x-www-form-urlencoded")
            {
                int filesize = (int)context.Request.ContentLength;
                NewRequestBackInfo backInfo = null;
                using (var file = new MemoryStream())
                {
                    int peekpos = 0;
                    byte[] buf = new byte[4096];
                    while (peekpos < filesize)
                    {
                        try
                        {
                            int read = await context.Request.Body.ReadAsync(buf, 0, buf.Length);
                            peekpos += read;
                            if (read > 0)
                            {
                                await file.WriteAsync(buf, 0, read);
                            }
                        }
                        catch (Exception err)
                        {
                            context.Response.StatusCode = 400;
                            await context.Response.WriteAsync(err.Message);
                            return;
                        }
                    }
                    string json = System.Text.Encoding.UTF8.GetString(file.ToArray());

                    NewRequestInfoData RequestInfo = JsonConvert.DeserializeObject<NewRequestInfoData>(json);
                    if (!string.IsNullOrEmpty(RequestInfo.AiGameOutputId))
                    {
                        AI_Game_Output item = AI_Game_Output.getByid(RequestInfo.AiGameOutputId);
                        if (item != null)
                        {

                            // 设置响应头
                            context.Response.StatusCode = 200;
                            context.Response.ContentType = "text/event-stream; charset=utf-8";
                            context.Response.Headers.Add("Cache-Control", "no-cache");
                            context.Response.Headers.Add("Connection", "keep-alive");
                            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

                            List<AI_Game_Output> alllist = AI_Game_Output.getAllDataToList();

                            // 筛选出所有ifJson值与concurrency相同的数据
                            List<AI_Game_Output> filteredList = alllist.Where(x => x.step == item.step && x.id != "10013").ToList();



                            // 输出筛选结果
                            Console.WriteLine($"找到 {filteredList.Count} 条step为{item.step}的记录:");
                            Dictionary<string, InputPromptData> dic = new Dictionary<string, InputPromptData>();
                            for (var i = 0; i < filteredList.Count; i++)
                            {
                                AI_Game_Output data = filteredList[i];
                                InputPromptData inputPromptData = new InputPromptData();

                                switch (data.id)
                                {
                                    case "10001":
                                        inputPromptData.systemPrompt = data.systemPrompt;
                                        inputPromptData.userPrompt = RequestInfo.userInputStr;
                                        break;
                                    case "10002":
                                        string outputPath = Path.Combine("baseTemplate", "GameBase.json");
                                        string jsonString = File.ReadAllText(outputPath);
                                        inputPromptData.systemPrompt = data.systemPrompt;
                                        inputPromptData.userPrompt = jsonString;
                                        break;
                                    case "10003":
                                        string prompt = item.generateTemplate;
                                        prompt = Regex.Replace(prompt, @"\{PlayerInt_题材\}", RequestInfo.theme);
                                        prompt = Regex.Replace(prompt, @"\{PlayerInt_游戏类型\}", RequestInfo.gametype);
                                        prompt = Regex.Replace(prompt, @"\{PlayerInt_游戏视角\}", RequestInfo.gameangleOfview);
                                        prompt = Regex.Replace(prompt, @"\{PlayerInt_dimension\}", RequestInfo.dimension);
                                        prompt = Regex.Replace(prompt, @"\{PlayerInt_世界观\}", RequestInfo.worldview);
                                        inputPromptData.systemPrompt = data.systemPrompt;
                                        inputPromptData.userPrompt = prompt;
                                        break;
                                }
                                dic.Add(data.id, inputPromptData);
                            }
                            // 创建多个对话
                            List<AiConversation> conversations = await AiConversation.NewCreateMultipleConversations(
                                context,
                            dic
                            );
                            // 将所有对话添加到字典中
                            foreach (var _conversation in conversations)
                            {
                                _conversationDic.TryAdd(_conversation.ConversationId, _conversation);
                            }
                            // 为第一个对话设置当前上下文并开始流式输出
                            if (conversations.Count > 0)
                            {
                                var firstConversation = conversations[0];
                                firstConversation.Context = context;
                                firstConversation.HttpcontextDic.TryAdd(context, false);

                                // 异步启动所有其他对话
                                for (int i = 1; i < conversations.Count; i++)
                                {
                                    string filename = "";
                                    AiConversation aiConversation = conversations[i];
                                    switch (aiConversation.baseId)
                                    {
                                        case "10001":
                                            filename = "GameBase";
                                            break;
                                        case "10002":
                                            filename = "WorldViewAndTarget";
                                            break;
                                    }
                                    conversations[i].StartGenerationTxtAsync(filename);
                                }

                                // 同步启动第一个对话
                                //await firstConversation.NewStreamDeepSeekResponseToClient();
                                string name = "";
                                AI_Game_Output item1 = AI_Game_Output.getByid(firstConversation.baseId);
                                switch (firstConversation.baseId)
                                {
                                    case "10001":
                                        name = "GameBase";
                                        break;
                                    case "10002":
                                        name = "WorldViewAndTarget";
                                        break;
                                }
                                if (item1.ifSse == true)
                                {
                                    await firstConversation.NewStreamDeepSeekResponseToClient();
                                    switch (item1.priority)
                                    {
                                        case "JSON":
                                            firstConversation.SystemPrompt = "";
                                            firstConversation.UserPrompt = firstConversation.ConversationContent + item.jsonModle;
                                            await firstConversation.GenerateTextContent(name);
                                            string mdStr = firstConversation.RemoveJSONCodeBlocks(firstConversation.TxtContent.ToString());
                                            string outputPath = Path.Combine(NewDeepSeekManager.Instance.baseTemplatePath, $"{name}.json");
                                            await File.WriteAllTextAsync(outputPath, mdStr.ToString());

                                            break;
                                        case "MARKDOWN":
                                            break;
                                    }
                                }
                                else
                                {
                                    await firstConversation.GenerateTextContent(name);
                                    switch (item1.priority)
                                    {
                                        case "JSON":
                                            firstConversation.SystemPrompt = "";
                                            firstConversation.UserPrompt = firstConversation.TxtContent + item.jsonModle;
                                            await firstConversation.NewStreamDeepSeekResponseToClient();
                                            string mdStr = firstConversation.RemoveJSONCodeBlocks(firstConversation.ConversationContent.ToString());
                                            string outputPath = Path.Combine(NewDeepSeekManager.Instance.baseTemplatePath, $"{name}.json");
                                            await File.WriteAllTextAsync(outputPath, mdStr.ToString());

                                            break;
                                        case "MARKDOWN":
                                            break;
                                    }
                                }

                            }
                            //RequestInfo.systemPrompt = item.systemPrompt;
                            //string filename = "";
                            //switch (RequestInfo.AiGameOutputId) {
                            //    case "10001":
                            //        filename = "GameBase";
                            //        break;
                            //    case "10002":
                            //        filename = "WorldViewAndTarget";
                            //        break;
                            //}

                            ////新建对话
                            //AiConversation conversation = await CreateOrGetConversation(context, RequestInfo);
                            //await conversation.GenerateTextContent( filename);
                            ////Console.WriteLine(txtContent);
                            //switch (item.priority)
                            //{
                            //    case "JSON":
                            //        conversation.SystemPrompt = "";
                            //        conversation.UserPrompt = conversation.TxtContent + item.jsonModle;
                            //        await conversation.NewStreamDeepSeekResponseToClient();
                            //        string mdStr = conversation.RemoveJSONCodeBlocks(conversation.ConversationContent.ToString());
                            //        string outputPath = Path.Combine(NewDeepSeekManager.Instance.baseTemplatePath, $"{filename}.json");
                            //        await File.WriteAllTextAsync(outputPath, mdStr.ToString());

                            //        break;
                            //    case "MARKDOWN":
                            //        break;
                            //}

                        }
                    }
                    //// 设置响应头
                    //context.Response.StatusCode = 200;
                    //context.Response.ContentType = "text/event-stream; charset=utf-8";
                    //context.Response.Headers.Add("Cache-Control", "no-cache");
                    //context.Response.Headers.Add("Connection", "keep-alive");
                    //context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                    ////新建对话
                    //AiConversation conversation = await CreateOrGetConversation(context, RequestInfo);
                    //switch (processtype)
                    //{
                    //    case NewProcessType.DeepSeekChat:
                    //        await conversation.NewStreamDeepSeekResponseToClient();
                    //        break;
                    //}
                }
            }
            else
            {
                await context.Response.WriteAsync("not support contentType=" + context.Request.ContentType);
            }
        }
        else
        {
            await context.Response.WriteAsync("error method");
        }
    }
    private static async Task<AiConversation> CreateOrGetConversation(HttpContext context, NewRequestInfoData RequestInfo)
    {
        AiConversation conversation = new AiConversation();
        // 生成唯一的对话ID，可以从请求中获取或生成新的
        string conversationId = context.Request.Query["conversationId"].ToString();
        if (string.IsNullOrEmpty(conversationId))
        {
            //conversationId = Guid.NewGuid().ToString();
            string serverIp = GetLocalIPAddress();
            DateTime currentTime = DateTime.Now;
            conversationId = $"userid-{serverIp}-{currentTime:yyyyMMddHHmmss}";

        }
        Console.WriteLine($"conversationid :{conversationId}");
        // 尝试获取现有对话，如果不存在则创建新对话
        return _conversationDic.GetOrAdd(conversationId, id =>
        {
            var conversation = new AiConversation
            {
                ConversationId = id,
                UserPrompt = RequestInfo.userInputStr,
                SystemPrompt = RequestInfo.systemPrompt,
                State = ConversationState.notStarted,
                Context = context,
                ConversationContent = new StringBuilder()
            };
            conversation.HttpcontextDic.TryAdd(context, false);
            return conversation;
        });
    }
    // 添加一个方法来获取所有活跃对话的列表
    static async Task onGetActiveConversations(HttpContext context)
    {
        var conversations = _conversationDic.Select(kvp => new
        {
            id = kvp.Key,
            state = kvp.Value.State.ToString(),
            contentLength = kvp.Value.ConversationContent.Length
        }).ToList();

        context.Response.StatusCode = 200;
        context.Response.ContentType = "application/json";
        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
        await context.Response.WriteAsync(JsonConvert.SerializeObject(new { conversations }));
    }
    /// <summary>
    /// 通过对话id 切换所获取的不同对话的内容
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    static async Task onGetConversation(HttpContext context)
    {
        string conversationId = context.Request.Query["conversationId"].ToString();
        if (string.IsNullOrEmpty(conversationId))
        {
            context.Response.StatusCode = 400;
            await context.Response.WriteAsync("Missing conversationId parameter");
            return;
        }
        if (_conversationDic.ContainsKey(conversationId))
        {
            AiConversation conversation = _conversationDic[conversationId];
            //conversation.Context = context;
            if (!conversation.HttpcontextDic.ContainsKey(context))
            {
                conversation.HttpcontextDic.TryAdd(context, false);
            }
            // 设置当前Context
            conversation.Context = context;
            await conversation.GetConversationContent();
        }
        else
        {
            context.Response.StatusCode = 404;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { error = "Conversation not found" }));
        }
    }

    // 添加取消对话的处理方法
    static async Task onCancelConversation(HttpContext context)
    {
        // 设置CORS头
        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

        if (context.Request.Method == "OPTIONS")
        {
            context.Response.StatusCode = 200;
            context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
            await context.Response.WriteAsync("");
            return;
        }

        string conversationId = context.Request.Query["conversationId"].ToString();
        if (string.IsNullOrEmpty(conversationId))
        {
            context.Response.StatusCode = 400;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { error = "Missing conversationId parameter" }));
            return;
        }

        if (_conversationDic.TryGetValue(conversationId, out AiConversation conversation))
        {
            conversation.CancelConversation();
            context.Response.StatusCode = 200;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { success = true, message = "对话已取消", state = conversation.State.ToString() }));
        }
        else
        {
            context.Response.StatusCode = 404;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { error = "Conversation not found" }));
        }
    }
    // 添加处理多对话请求的方法
    static async Task onMultiChat(HttpContext context)
    {
        if (context.Request.Method == "OPTIONS")
        {
            context.Response.StatusCode = 200;
            context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
            await context.Response.WriteAsync("");
            return;
        }

        if (context.Request.Method != "POST")
        {
            context.Response.StatusCode = 405;
            await context.Response.WriteAsync("Method not allowed");
            return;
        }

        try
        {
            // 读取请求体
            string requestBody;
            using (var reader = new StreamReader(context.Request.Body))
            {
                requestBody = await reader.ReadToEndAsync();
            }

            // 解析请求数据
            var requestData = JsonConvert.DeserializeObject<MultiChatRequest>(requestBody);

            // 如果没有提供prompts，使用默认的三个prompt
            if (requestData.prompts == null || requestData.prompts.Count == 0)
            {
                requestData.prompts = new List<string>
                {
                    "做一个中华上下五千年历史发展的时间事件列表，比如：公元前xxx年，发生了什么事件，主要代表人物，事件的主要影响。",
                    "详细说明相对论的发展",
                    "帮我翻译一下出师表的全部内容，翻译前对作者背景也做出详细介绍，翻译过程中，对一些特别要注意的字词或者是句型，也要做出批注，方便我查看理解"
                };
            }

            // 创建多个对话
            List<AiConversation> conversations = await AiConversation.CreateMultipleConversations(
                context,
            requestData.prompts,
            requestData.systemPrompt
            );

            // 将所有对话添加到字典中
            foreach (var conversation in conversations)
            {
                _conversationDic.TryAdd(conversation.ConversationId, conversation);
            }

            // 设置SSE响应头
            context.Response.ContentType = "text/event-stream; charset=utf-8";
            context.Response.Headers.Add("Cache-Control", "no-cache");
            context.Response.Headers.Add("Connection", "keep-alive");
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

            // 首先发送所有对话的ID
            var conversationIds = conversations.Select(c => c.ConversationId).ToList();
            await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { conversationIds })}\n\n");
            await context.Response.Body.FlushAsync();

            // 为第一个对话设置当前上下文并开始流式输出
            if (conversations.Count > 0)
            {
                var firstConversation = conversations[0];
                firstConversation.Context = context;
                firstConversation.HttpcontextDic.TryAdd(context, false);

                // 异步启动所有其他对话
                for (int i = 1; i < conversations.Count; i++)
                {
                    conversations[i].StartGenerationAsync();
                }

                // 同步启动第一个对话并流式输出
                await firstConversation.NewStreamDeepSeekResponseToClient();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理多对话请求时出错: {ex.Message}");
            if (!context.Response.HasStarted)
            {
                context.Response.StatusCode = 500;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync(JsonConvert.SerializeObject(new { error = ex.Message }));
            }
        }
    }
    //public async Task NewMultiChat(Dictionary<string,string> promptDic) {
    //    // 创建多个对话
    //    List<AiConversation> conversations = await AiConversation.NewCreateMultipleConversations(promptDic);

    //}

    public static string GetLocalIPAddress()
    {
        var host = Dns.GetHostEntry(Dns.GetHostName());
        foreach (var ip in host.AddressList)
        {
            if (ip.AddressFamily == AddressFamily.InterNetwork)
            {
                return ip.ToString();
            }
        }
        throw new Exception("No network adapters with an IPv4 address in the system!");
    }


    enum NewProcessType
    {
        DeepSeekChat,
        StreamOutput

    }
    public class NewRequestInfoData
    {
        public string userInputStr;
        public string theme;
        public string gametype;
        public string gameangleOfview;
        public string dimension;
        public string worldview;
        public string systemPrompt;
        public string AiGameOutputId;
        public string DesignedId;
    }
    public class NewRequestBackInfo
    {
        public string result;
        public int recode;
        public string content;
        public string ignJson;
        public string gameIntroduceJson;
    }

    // 添加多对话请求的数据模型
    public class MultiChatRequest
    {
        public List<string> prompts { get; set; }
        public string systemPrompt { get; set; }
    }

    #region RESTful API 处理方法

    /// <summary>
    /// 处理获取 RESTful API 描述的请求
    /// </summary>
    static async Task onGetRestfulApi(HttpContext context)
    {
        try
        {
            // 设置 CORS 头
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            context.Response.ContentType = "application/json; charset=utf-8";

            var apiDescription = AccountManageAPI.GetHttpPostFunction();
            await context.Response.WriteAsync(apiDescription);
        }
        catch (Exception ex)
        {
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json; charset=utf-8";
            var errorResponse = JsonConvert.SerializeObject(new { error = ex.Message });
            await context.Response.WriteAsync(errorResponse);
        }
    }

    /// <summary>
    /// 处理获取 API 测试页面的请求
    /// </summary>
    static async Task onGetApiTestPage(HttpContext context)
    {
        try
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "api-test.html");

            if (File.Exists(filePath))
            {
                context.Response.ContentType = "text/html; charset=utf-8";
                var fileContent = await File.ReadAllTextAsync(filePath);
                await context.Response.WriteAsync(fileContent);
            }
            else
            {
                context.Response.StatusCode = 404;
                context.Response.ContentType = "text/plain";
                await context.Response.WriteAsync("API test page not found");
            }
        }
        catch (Exception ex)
        {
            context.Response.StatusCode = 500;
            context.Response.ContentType = "text/plain";
            await context.Response.WriteAsync($"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理账号管理 API 请求
    /// </summary>
    static async Task onAccountApi(HttpContext context)
    {
        try
        {
            // 设置 CORS 头
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            // 处理 OPTIONS 预检请求
            if (context.Request.Method == "OPTIONS")
            {
                context.Response.StatusCode = 200;
                return;
            }

            var path = context.Request.Path.Value ?? "";

            // 检查是否是账号管理 API 路径
            if (!path.StartsWith("/api/account"))
            {
                context.Response.StatusCode = 404;
                context.Response.ContentType = "application/json; charset=utf-8";
                var errorResponse = JsonConvert.SerializeObject(new { error = "API endpoint not found" });
                await context.Response.WriteAsync(errorResponse);
                return;
            }

            // 提取方法名
            string methodName;
            if (path == "/api/account")
            {
                // 如果是根路径，从请求体中获取方法名
                methodName = "";
            }
            else if (path.StartsWith("/api/account/"))
            {
                methodName = path.Substring("/api/account/".Length).ToLower();
            }
            else
            {
                context.Response.StatusCode = 404;
                context.Response.ContentType = "application/json; charset=utf-8";
                var errorResponse = JsonConvert.SerializeObject(new { error = "Invalid API path" });
                await context.Response.WriteAsync(errorResponse);
                return;
            }

            // 读取请求体
            string requestBody;
            using (var reader = new StreamReader(context.Request.Body))
            {
                requestBody = await reader.ReadToEndAsync();
            }

            // 解析请求参数
            var requestData = string.IsNullOrEmpty(requestBody)
                ? new Dictionary<string, object>()
                : JsonConvert.DeserializeObject<Dictionary<string, object>>(requestBody) ?? new Dictionary<string, object>();

            // 如果方法名为空，尝试从请求体中获取
            if (string.IsNullOrEmpty(methodName))
            {
                if (requestData.ContainsKey("method"))
                {
                    methodName = requestData["method"]?.ToString() ?? "";
                    requestData.Remove("method"); // 移除方法名，避免作为参数传递

                    // 如果有 parameters 字段，使用它作为参数
                    if (requestData.ContainsKey("parameters") && requestData["parameters"] is Newtonsoft.Json.Linq.JObject parametersObj)
                    {
                        requestData = parametersObj.ToObject<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                    }
                }
                else
                {
                    context.Response.StatusCode = 400;
                    context.Response.ContentType = "application/json; charset=utf-8";
                    var errorResponse = JsonConvert.SerializeObject(new { error = "Method name is required in request body or URL path" });
                    await context.Response.WriteAsync(errorResponse);
                    return;
                }
            }

            if (string.IsNullOrEmpty(methodName))
            {
                context.Response.StatusCode = 400;
                context.Response.ContentType = "application/json; charset=utf-8";
                var errorResponse = JsonConvert.SerializeObject(new { error = "Method name is required" });
                await context.Response.WriteAsync(errorResponse);
                return;
            }

            // 调用对应的 AccountManage 方法
            var result = await CallAccountManageMethod(methodName, requestData);

            context.Response.ContentType = "application/json; charset=utf-8";
            var jsonResponse = JsonConvert.SerializeObject(result, Formatting.Indented);
            await context.Response.WriteAsync(jsonResponse);
        }
        catch (Exception ex)
        {
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json; charset=utf-8";
            var errorResponse = JsonConvert.SerializeObject(new { error = ex.Message, stackTrace = ex.StackTrace });
            await context.Response.WriteAsync(errorResponse);
        }
    }

    /// <summary>
    /// 调用 AccountManage 的方法
    /// </summary>
    private static async Task<object> CallAccountManageMethod(string methodName, Dictionary<string, object> requestData)
    {
        try
        {
            var accountManageType = typeof(AccountManage);

            // 查找匹配的方法（忽略大小写）
            var method = accountManageType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                .FirstOrDefault(m => m.Name.ToLower() == methodName.ToLower() && m.DeclaringType == accountManageType);

            if (method == null)
            {
                return new { error = $"Method '{methodName}' not found" };
            }

            // 获取方法参数
            var parameters = method.GetParameters();
            var args = new object[parameters.Length];

            // 填充参数值
            for (int i = 0; i < parameters.Length; i++)
            {
                var param = parameters[i];
                var paramName = param.Name ?? "";

                if (requestData.ContainsKey(paramName))
                {
                    var value = requestData[paramName];
                    args[i] = ConvertParameter(value, param.ParameterType);
                }
                else if (param.HasDefaultValue)
                {
                    args[i] = param.DefaultValue;
                }
                else
                {
                    // 必需参数但未提供值
                    return new { error = $"Required parameter '{paramName}' is missing" };
                }
            }

            // 调用方法
            var result = method.Invoke(null, args);

            // 如果是异步方法，等待结果
            if (result is Task task)
            {
                await task;
                var resultProperty = task.GetType().GetProperty("Result");
                if (resultProperty != null)
                {
                    result = resultProperty.GetValue(task);
                }
            }

            return result ?? new { message = "Method executed successfully" };
        }
        catch (Exception ex)
        {
            return new { error = ex.Message, stackTrace = ex.StackTrace };
        }
    }

    /// <summary>
    /// 转换参数类型
    /// </summary>
    private static object? ConvertParameter(object? value, Type targetType)
    {
        if (value == null)
            return null;

        // 处理可空类型
        if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            targetType = targetType.GetGenericArguments()[0];
        }

        // 如果类型已经匹配，直接返回
        if (targetType.IsAssignableFrom(value.GetType()))
            return value;

        // 处理字符串类型
        if (targetType == typeof(string))
            return value.ToString();

        // 处理数值类型
        if (targetType == typeof(int))
            return Convert.ToInt32(value);
        if (targetType == typeof(long))
            return Convert.ToInt64(value);
        if (targetType == typeof(bool))
            return Convert.ToBoolean(value);
        if (targetType == typeof(double))
            return Convert.ToDouble(value);
        if (targetType == typeof(float))
            return Convert.ToSingle(value);
        if (targetType == typeof(decimal))
            return Convert.ToDecimal(value);

        // 处理数组类型
        if (targetType.IsArray)
        {
            var elementType = targetType.GetElementType()!;
            if (value is Newtonsoft.Json.Linq.JArray jArray)
            {
                var array = System.Array.CreateInstance(elementType, jArray.Count);
                for (int i = 0; i < jArray.Count; i++)
                {
                    var convertedElement = ConvertParameter(jArray[i], elementType);
                    array.SetValue(convertedElement, i);
                }
                return array;
            }
        }

        // 尝试使用 Convert.ChangeType
        try
        {
            return Convert.ChangeType(value, targetType);
        }
        catch
        {
            // 如果转换失败，返回原值
            return value;
        }
    }

    #endregion
}
