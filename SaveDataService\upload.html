<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传系统 - 支持断点续传</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-input {
            margin: 10px 0;
        }
        .file-input input[type="file"] {
            display: none;
        }
        .file-input label {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .file-input label:hover {
            background-color: #0056b3;
        }
        .progress-container {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            text-align: center;
            margin-top: 5px;
            font-size: 14px;
            color: #666;
        }
        .file-list {
            margin-top: 20px;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .file-info {
            flex: 1;
        }
        .file-name {
            font-weight: bold;
            color: #333;
        }
        .file-size {
            font-size: 12px;
            color: #666;
        }
        .file-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 5px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .move-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .move-section h3 {
            margin-top: 0;
            color: #333;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件上传系统</h1>
        
        <!-- 单文件上传（支持断点续传） -->
        <div class="upload-section" id="singleUpload">
            <h3>单文件上传（支持断点续传）</h3>
            <div class="file-input">
                <label for="singleFile">选择文件</label>
                <input type="file" id="singleFile" />
            </div>
            <p>或拖拽文件到此区域</p>
            <div class="progress-container" id="singleProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="singleProgressFill"></div>
                </div>
                <div class="progress-text" id="singleProgressText">0%</div>
            </div>
        </div>

        <!-- 文件夹上传 -->
        <div class="upload-section">
            <h3>文件夹批量上传</h3>
            <div class="file-input">
                <label for="folderFiles">选择文件夹</label>
                <input type="file" id="folderFiles" webkitdirectory multiple />
            </div>
            <div class="progress-container" id="folderProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="folderProgressFill"></div>
                </div>
                <div class="progress-text" id="folderProgressText">0%</div>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-list" id="fileList"></div>

        <!-- 状态显示 -->
        <div id="status"></div>

        <!-- HTML文件移动功能 -->
        <div class="move-section">
            <h3>HTML文件移动</h3>
            <div class="input-group">
                <label for="sourcePath">源文件路径:</label>
                <input type="text" id="sourcePath" placeholder="例如: upload.html" />
            </div>
            <div class="input-group">
                <label for="targetPath">目标路径:</label>
                <input type="text" id="targetPath" placeholder="例如: pages/upload.html" />
            </div>
            <button class="btn btn-warning" onclick="moveFile()">移动文件</button>
        </div>
    </div>

    <script>
        // 全局变量
        let uploadSessions = new Map();
        const CHUNK_SIZE = 1024 * 1024; // 1MB chunks

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        function setupEventListeners() {
            // 单文件上传
            document.getElementById('singleFile').addEventListener('change', handleSingleFileSelect);
            
            // 文件夹上传
            document.getElementById('folderFiles').addEventListener('change', handleFolderSelect);
            
            // 拖拽上传
            const singleUpload = document.getElementById('singleUpload');
            singleUpload.addEventListener('dragover', handleDragOver);
            singleUpload.addEventListener('dragleave', handleDragLeave);
            singleUpload.addEventListener('drop', handleDrop);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadSingleFile(files[0]);
            }
        }

        function handleSingleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                uploadSingleFile(file);
            }
        }

        function handleFolderSelect(e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                uploadFolder(files);
            }
        }

        // 计算文件MD5哈希
        async function calculateFileHash(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const hash = btoa(file.name + file.size + file.lastModified).replace(/[^a-zA-Z0-9]/g, '');
                    resolve(hash);
                };
                reader.readAsArrayBuffer(file.slice(0, Math.min(file.size, 1024)));
            });
        }

        // 单文件上传（支持断点续传）
        async function uploadSingleFile(file) {
            const fileHash = await calculateFileHash(file);
            const sessionId = `${file.name}_${fileHash}`;

            showStatus(`开始上传文件: ${file.name}`, 'info');
            showProgress('single', 0);

            try {
                await uploadFileWithResume(file, sessionId, 'single');
                showStatus(`文件上传成功: ${file.name}`, 'success');
            } catch (error) {
                showStatus(`文件上传失败: ${error.message}`, 'error');
            }
        }

        // 断点续传上传文件
        async function uploadFileWithResume(file, sessionId, progressType) {
            let uploadedSize = 0;
            const totalSize = file.size;
            const fileHash = await calculateFileHash(file);
            let retryCount = 0;
            const maxRetries = 3;

            while (uploadedSize < totalSize) {
                const chunkStart = uploadedSize;
                const chunkEnd = Math.min(uploadedSize + CHUNK_SIZE, totalSize);
                const chunk = file.slice(chunkStart, chunkEnd);

                try {
                    console.log(`上传块: ${chunkStart}-${chunkEnd-1}/${totalSize}`);
                    const response = await uploadChunk(chunk, file.name, totalSize, chunkStart, chunkEnd - 1, fileHash);

                    if (response.success) {
                        // 更新已上传大小，确保不会重复上传
                        const serverUploadedSize = response.uploadedSize;
                        uploadedSize = Math.max(uploadedSize, serverUploadedSize);
                        const progress = (uploadedSize / totalSize) * 100;
                        showProgress(progressType, progress);

                        console.log(`上传进度: ${uploadedSize}/${totalSize} (${progress.toFixed(1)}%)`);
                        console.log(`服务器响应:`, response);
                        console.log(`下一个块将从 ${uploadedSize} 开始`);

                        if (response.isCompleted) {
                            console.log('文件上传完成');
                            showStatus(`文件上传完成: ${file.name}`, 'success');
                            break;
                        }

                        // 重置重试计数
                        retryCount = 0;
                    } else {
                        console.error('服务器返回错误:', response);
                        throw new Error(response.message || '上传失败');
                    }
                } catch (error) {
                    console.error('Upload chunk failed:', error);
                    retryCount++;

                    if (retryCount >= maxRetries) {
                        throw new Error(`上传失败，已重试${maxRetries}次: ${error.message}`);
                    }

                    // 等待递增时间后重试
                    await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                }
            }
        }

        // 上传文件块
        async function uploadChunk(chunk, fileName, totalSize, startByte, endByte, fileHash) {
            const response = await fetch('/api/upload', {
                method: 'POST',
                headers: {
                    'X-File-Name': encodeURIComponent(fileName),
                    'X-File-Size': totalSize.toString(),
                    'X-File-Hash': fileHash,
                    'Content-Range': `bytes ${startByte}-${endByte}/${totalSize}`,
                    'Content-Type': 'application/octet-stream'
                },
                body: chunk
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        }

        // 文件夹上传
        async function uploadFolder(files) {
            showStatus(`开始上传文件夹，共 ${files.length} 个文件`, 'info');
            showProgress('folder', 0);

            const formData = new FormData();
            files.forEach(file => {
                formData.append('files', file, file.webkitRelativePath || file.name);
            });

            try {
                const response = await fetch('/api/upload/folder', {
                    method: 'POST',
                    headers: {
                        'X-Folder-Path': 'uploaded_folder'
                    },
                    body: formData
                });

                const result = await response.json();
                console.log('文件夹上传响应:', result);

                if (result.success) {
                    showProgress('folder', 100);
                    showStatus(`文件夹上传完成，成功上传 ${result.results.filter(r => r.success).length} 个文件`, 'success');
                    displayUploadResults(result.results);
                } else {
                    console.error('文件夹上传失败，服务器响应:', result);
                    showStatus(`文件夹上传失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus(`文件夹上传失败: ${error.message}`, 'error');
            }
        }

        // 移动文件
        async function moveFile() {
            const sourcePath = document.getElementById('sourcePath').value.trim();
            const targetPath = document.getElementById('targetPath').value.trim();

            if (!sourcePath || !targetPath) {
                showStatus('请填写源文件路径和目标路径', 'error');
                return;
            }

            try {
                const response = await fetch('/api/move', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sourcePath: sourcePath,
                        targetPath: targetPath
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(`文件移动成功: ${sourcePath} -> ${targetPath}`, 'success');
                    document.getElementById('sourcePath').value = '';
                    document.getElementById('targetPath').value = '';
                } else {
                    showStatus(`文件移动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`文件移动失败: ${error.message}`, 'error');
            }
        }

        // 显示进度
        function showProgress(type, percentage) {
            const progressContainer = document.getElementById(`${type}Progress`);
            const progressFill = document.getElementById(`${type}ProgressFill`);
            const progressText = document.getElementById(`${type}ProgressText`);

            progressContainer.style.display = 'block';
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${Math.round(percentage)}%`;
        }

        // 显示状态消息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';

            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 显示上传结果
        function displayUploadResults(results) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '<h3>上传结果:</h3>';

            results.forEach(result => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">${result.fileName}</div>
                        <div class="file-size">${result.success ? '上传成功' : '上传失败: ' + result.message}</div>
                    </div>
                    <div class="file-actions">
                        <span class="btn ${result.success ? 'btn-success' : 'btn-danger'}">
                            ${result.success ? '✓' : '✗'}
                        </span>
                    </div>
                `;
                fileList.appendChild(fileItem);
            });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
